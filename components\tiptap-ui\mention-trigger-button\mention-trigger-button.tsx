'use client';

import * as React from 'react';
// --- Tiptap UI ---
import type { UseMentionTriggerConfig } from '@/components/tiptap-ui/mention-trigger-button';
import {
  MENTION_TRIGGER_SHORTCUT_KEY,
  useMentionTrigger,
} from '@/components/tiptap-ui/mention-trigger-button';
import { Badge } from '@/components/tiptap-ui-primitive/badge';
// --- UI Primitives ---
import type { ButtonProps } from '@/components/tiptap-ui-primitive/button';
import { Button } from '@/components/tiptap-ui-primitive/button';
// --- Hooks ---
import { useTiptapEditor } from '@/hooks/use-tiptap-editor';
// --- Lib ---
import { parseShortcutKeys } from '@/lib/tiptap-utils';

export interface MentionTriggerButtonProps
  extends Omit<ButtonProps, 'type'>,
    UseMentionTriggerConfig {
  /**
   * Optional text to display alongside the icon.
   */
  text?: string;
  /**
   * Optional show shortcut keys in the button.
   * @default false
   */
  showShortcut?: boolean;
}

export function MentionShortcutBadge({
  shortcutKeys = MENTION_TRIGGER_SHORTCUT_KEY,
}: {
  shortcutKeys?: string;
}) {
  return <Badge>{parseShortcutKeys({ shortcutKeys })}</Badge>;
}

/**
 * Button component for inserting mention triggers in a Tiptap editor.
 *
 * For custom button implementations, use the `useMention` hook instead.
 */
export const MentionTriggerButton = React.forwardRef<
  HTMLButtonElement,
  MentionTriggerButtonProps
>(
  (
    {
      editor: providedEditor,
      node,
      nodePos,
      text,
      trigger = '@',
      hideWhenUnavailable = false,
      onTriggered,
      showShortcut = false,
      onClick,
      children,
      ...buttonProps
    },
    ref
  ) => {
    const { editor } = useTiptapEditor(providedEditor);
    const { isVisible, canInsert, handleMention, label, shortcutKeys, Icon } =
      useMentionTrigger({
        editor,
        node,
        nodePos,
        trigger,
        hideWhenUnavailable,
        onTriggered,
      });

    const handleClick = React.useCallback(
      (event: React.MouseEvent<HTMLButtonElement>) => {
        onClick?.(event);
        if (event.defaultPrevented) return;
        handleMention();
      },
      [handleMention, onClick]
    );

    if (!isVisible) {
      return null;
    }

    return (
      <Button
        aria-label={label}
        data-disabled={!canInsert}
        data-style="ghost"
        disabled={!canInsert}
        onClick={handleClick}
        role="button"
        tabIndex={-1}
        tooltip={label}
        type="button"
        {...buttonProps}
        ref={ref}
      >
        {children ?? (
          <>
            <Icon className="tiptap-button-icon" />
            {text && <span className="tiptap-button-text">{text}</span>}
            {showShortcut && (
              <MentionShortcutBadge shortcutKeys={shortcutKeys} />
            )}
          </>
        )}
      </Button>
    );
  }
);

MentionTriggerButton.displayName = 'MentionTriggerButton';
