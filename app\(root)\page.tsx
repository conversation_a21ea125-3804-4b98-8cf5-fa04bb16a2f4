import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import RedirectWithRole from './redirect-with-role';

export default async function Home() {
  const user = await fetchQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );
  if (!user) {
    return notFound();
  }
  // if i have user status is authenticated so assign status to authenticated
  const status = 'authenticated';

  return <RedirectWithRole role={user.role ?? null} status={status} />;
}
