import { v } from "convex/values";
import { api, internal } from "./_generated/api";
import { internalMutation, mutation, query } from "./_generated/server";
import { requireUser } from "./users";
import { r2 } from "./media";
import { canUpdateArticle } from "./helpers/articleHelper";

export const createArticle = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const slug = args.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-|-$/g, "");
      const articleId = await ctx.db.insert("articles", {
        ...args,
        slug,
        userId,
        status: "draft",
        isFavorite: false,
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: args.title,
        docStatus: "draft",
        action: "created" as const,
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to create article." };
    }
  },
});
// add new article from editor
export const addNewArticle = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    content: v.optional(v.string()),
    words: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const slug = args.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-|-$/g, "");
      const articleId = await ctx.db.insert("articles", {
        ...args,
        slug,
        userId,
        status: "draft",
        isFavorite: false,
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: args.title,
        docStatus: "draft",
        action: "created" as const,
      });
      return { success: true, data: { id: articleId, title: args.title } };
    } catch {
      return { success: false, error: "Failed to create article." };
    }
  },
});

export const updateArticleContent = mutation({
  args: {
    id: v.id("articles"),
    title: v.string(),
    description: v.optional(v.string()),
    content: v.optional(v.string()),
    words: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // ✅ central check
      const { allowed, error, article } = await canUpdateArticle(ctx, args.id);
      if (!allowed || !article) {
        return { success: false, error };
      }

      const slug =
        args.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-|-$/g, "") || `untitled-${Date.now()}`;

      await ctx.db.patch(args.id, {
        title: args.title,
        description: args.description,
        content: args.content,
        words: args.words,
        slug,
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: article.status,
        action: "updated" as const,
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to update article." };
    }
  },
});
export const updateArticle = mutation({
  args: {
    id: v.id("articles"),
    title: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const article = await ctx.db.get(args.id);

      if (!article) {
        return { success: false, error: "Article not found." };
      }

      if (article.userId !== userId) {
        return { success: false, error: "Unauthorized." };
      }

      const slug =
        args.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-|-$/g, "") || `untitled-${Date.now()}`;

      await ctx.db.patch(args.id, {
        title: args.title,
        description: args.description,
        slug,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: article.status,
        action: "updated" as const,
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to update article." };
    }
  },
});

export const getArticles = query({
  args: {},
  handler: async (ctx) => {
    try {
      const userId = await requireUser(ctx);
      const articles = await ctx.db
        .query("articles")
        .withIndex("by_userId", (q) => q.eq("userId", userId))
        .collect();
      return articles;
    } catch {
      return { success: false, error: "Failed to fetch articles." };
    }
  },
});

export const getArticle = query({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      if (article.userId !== userId) {
        return { success: false, error: "Unauthorized." };
      }

      return article;
    } catch {
      return { success: false, error: "Failed to fetch article." };
    }
  },
});

export const deleteArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "deleted",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "deleted" as const,
      });
      await ctx.scheduler.runAfter(
        1000 * 60 * 60 * 24 * 7, // 7 days
        // 1000 * 2,
        internal.articles.removeDeletedArticles,
        {}
      );
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete article." };
    }
  },
});

export const restoreArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "draft",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "restored" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to restore article." };
    }
  },
});

export const toggleFavoriteArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }

      await ctx.db.patch(args.id, {
        isFavorite: !article.isFavorite,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: article.isFavorite
          ? ("removed-from-favorite" as const)
          : ("added-to-favorite" as const),
      });

      return { success: true };
    } catch {
      return { success: false, error: "Failed to toggle favorite article." };
    }
  },
});

export const stageArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "staged",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "staged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to stage article." };
    }
  },
});
export const unStageArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const article = await ctx.db.get(args.id);
      if (!article) {
        return { success: false, error: "Article not found." };
      }
      await ctx.db.patch(article._id, {
        status: "draft",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "unstaged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to unstage article." };
    }
  },
});

export const removeDeletedArticles = internalMutation({
  args: {},
  handler: async (ctx) => {
    const articles = await ctx.db
      .query("articles")
      .withIndex("by_status", (q) => q.eq("status", "deleted"))
      .collect();
    for (const article of articles) {
      await ctx.db.delete(article._id);
    }
  },
});

export const searchArticles = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const articles = await ctx.db
        .query("articles")
        .withSearchIndex("search_title", (q) => q.search("title", query))
        .collect();
      return articles;
    } catch {
      return { success: false, error: "Failed to search articles." };
    }
  },
});

export const getArticlesAnalytics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await requireUser(ctx);

    const articles = await ctx.db
      .query("articles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .collect();

    return articles.map((article) => ({
      date: new Date(article.updatedAt || article._creationTime)
        .toISOString()
        .split("T")[0], // 'YYYY-MM-DD'
      status: article.status,
    }));
  },
});

export const getTotalArticlesByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("staged"),
      v.literal("approved"),
      v.literal("published"),
      v.literal("deleted")
    ),
  },
  handler: async (ctx, { status }) => {
    const userId = await requireUser(ctx);

    const total = await ctx.db
      .query("articles")
      .withIndex("by_status_userId", (q) =>
        q.eq("status", status).eq("userId", userId)
      )
      .collect()
      .then((articles) => articles.length);

    return total;
  },
});

export const getApprovedMediaFiles = query({
  args: {},
  handler: async (ctx) => {
    try {
      const images = await ctx.db
        .query("mediaFiles")
        .withIndex("by_status", (q) => q.eq("status", "approved"))
        .collect();
      const metadata = await Promise.all(
        images.map(async (image) => {
          const metadata = await r2.getMetadata(ctx, image.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata ? metadata.contentType : "image/jpeg",
          };
        })
      );
      return Promise.all(
        images.map(async (image) => ({
          ...image,
          size: metadata[images.indexOf(image)].size,
          contentType: metadata[images.indexOf(image)].contentType,
          url: await r2.getUrl(image.key),
        }))
      );
    } catch {
      return { success: false, error: "Failed to fetch approved images." };
    }
  },
});
