// convex/helpers/canUpdateArticle.ts
import { Id } from "../_generated/dataModel";
import { QueryCtx } from "../_generated/server";
import { requireUser } from "../users";

export async function canUpdateArticle(
  ctx: QueryCtx,
  articleId: Id<"articles">
) {
  const userId = await requireUser(ctx);
  const article = await ctx.db.get(articleId);
  if (!article) {
    return { allowed: false, error: "Article not found." };
  }

  const user = await ctx.db.get(userId);
  if (!user) {
    return { allowed: false, error: "User not found." };
  }

  const isOwner = article.userId === userId;
  const isAdmin = user.role === "admin";

  if (!isOwner && !isAdmin) {
    return { allowed: false, error: "Unauthorized." };
  }

  return { allowed: true, article };
}
