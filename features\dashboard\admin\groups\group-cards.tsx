'use client';
import { useQuery } from 'convex/react';
import { useEffect, useState } from 'react';
import { api } from '@/convex/_generated/api';
import ViewToggle from '../../shared/components/view-toggle';
import SearchTop from '../../shared/search/search-top';
import CreateOrUpdateGroup from './create-update-group';
import GroupCard from './group-card';
import GroupTable from './groups-table';

export default function GroupCards() {
  const [searchText, setSearchText] = useState('');
  const [view, setView] = useState<'grid' | 'table'>('grid');
  const allgroups = useQuery(api.groups.getGroups) || [];
  const searchResults =
    useQuery(api.groups.searchGroups, {
      query: searchText,
    }) || [];

  useEffect(() => {
    const storedView = localStorage.getItem('articleView');
    if (storedView === 'grid' || storedView === 'table') {
      setView(storedView);
    } else {
      localStorage.setItem('articleView', 'grid');
    }
  }, []);
  const groups = searchText ? searchResults : allgroups;
  if (!(groups && Array.isArray(groups))) {
    return null;
  }
  return (
    <>
      <div className="flex items-center justify-between gap-2">
        <SearchTop
          searchPlaceholder="Search Groups..."
          searchText={searchText}
          setSearchText={setSearchText}
        />

        <ViewToggle setView={setView} view={view} />
        <CreateOrUpdateGroup />
      </div>
      {view === 'grid' ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {groups.map((group) => (
            <GroupCard group={group} key={group._id} />
          ))}
          {groups.length === 0 && (
            <div className="col-span-full flex flex-col items-center justify-center gap-2">
              <p className="text-muted-foreground text-sm">No groups found.</p>
            </div>
          )}
        </div>
      ) : (
        <GroupTable groups={groups} />
      )}
    </>
  );
}
