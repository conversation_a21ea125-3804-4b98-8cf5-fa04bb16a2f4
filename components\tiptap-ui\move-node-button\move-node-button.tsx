'use client';

import * as React from 'react';
// --- Tiptap UI ---
import type { UseMoveNodeConfig } from '@/components/tiptap-ui/move-node-button';
import { useMoveNode } from '@/components/tiptap-ui/move-node-button';
import { Badge } from '@/components/tiptap-ui-primitive/badge';
// --- UI Primitives ---
import type { ButtonProps } from '@/components/tiptap-ui-primitive/button';
import { Button } from '@/components/tiptap-ui-primitive/button';
// --- Hooks ---
import { useTiptapEditor } from '@/hooks/use-tiptap-editor';
// --- Lib ---
import { parseShortcutKeys } from '@/lib/tiptap-utils';

export interface MoveNodeButtonProps
  extends Omit<ButtonProps, 'type'>,
    UseMoveNodeConfig {
  /**
   * Optional text to display alongside the icon.
   */
  text?: string;
  /**
   * Optional show shortcut keys in the button.
   * @default false
   */
  showShortcut?: boolean;
}

export function MoveNodeShortcutBadge({
  shortcutKeys,
}: {
  shortcutKeys: string;
}) {
  return <Badge>{parseShortcutKeys({ shortcutKeys })}</Badge>;
}

/**
 * Button component for moving a node up or down in a Tiptap editor.
 */
export const MoveNodeButton = React.forwardRef<
  HTMLButtonElement,
  MoveNodeButtonProps
>(
  (
    {
      editor: providedEditor,
      text,
      direction,
      hideWhenUnavailable = false,
      onMoved,
      showShortcut = false,
      onClick,
      children,
      ...buttonProps
    },
    ref
  ) => {
    const { editor } = useTiptapEditor(providedEditor);
    const {
      isVisible,
      handleMoveNode,
      canMoveNode,
      label,
      shortcutKeys,
      Icon,
    } = useMoveNode({
      editor,
      direction,
      hideWhenUnavailable,
      onMoved,
    });

    const handleClick = React.useCallback(
      (event: React.MouseEvent<HTMLButtonElement>) => {
        onClick?.(event);
        if (event.defaultPrevented) return;
        handleMoveNode();
      },
      [handleMoveNode, onClick]
    );

    if (!isVisible) {
      return null;
    }

    const tooltip = direction === 'up' ? 'Move Up' : 'Move Down';

    return (
      <Button
        aria-label={label}
        data-style="ghost"
        disabled={!canMoveNode}
        onClick={handleClick}
        role="button"
        tabIndex={-1}
        tooltip={tooltip}
        type="button"
        {...buttonProps}
        ref={ref}
      >
        {children ?? (
          <>
            <Icon className="tiptap-button-icon" />
            {text && <span className="tiptap-button-text">{text}</span>}
            {showShortcut && (
              <MoveNodeShortcutBadge shortcutKeys={shortcutKeys} />
            )}
          </>
        )}
      </Button>
    );
  }
);

MoveNodeButton.displayName = 'MoveNodeButton';
