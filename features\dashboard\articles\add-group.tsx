import { useMutation, useQuery } from 'convex/react';
import { BoxIcon, SquareXIcon } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';

export default function AddGroupToArticle({
  articleId,
  prevGroupId,
}: {
  articleId: Id<'articles'>;
  prevGroupId: Id<'groups'>;
}) {
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const groups = useQuery(api.groups.getGroups);
  const addGroupToArticle = useMutation(api.groups.addGroupToArticle);
  const removeGroupFromArticle = useMutation(api.groups.removeGroupFromArticle);
  const handleAddGroupToArticle = async (
    id: Id<'articles'>,
    groupId: Id<'groups'>
  ) => {
    try {
      await addGroupToArticle({ articleId: id, groupId });
      toast.success('Group added successfully!');
    } catch {
      toast.error('Failed to add group.');
    }
  };
  const handleRemoveGroupFromArticle = async (id: Id<'articles'>) => {
    try {
      await removeGroupFromArticle({ articleId: id });
      toast.success('Group removed successfully!');
    } catch {
      toast.error('Failed to remove group.');
    }
  };

  const runCommand = useCallback((command: () => unknown) => {
    setOpen(false);
    command();
  }, []);
  const hasGroup = Boolean(prevGroupId);
  if (!groups || 'success' in groups) {
    return null;
  }
  return (
    <>
      {hasGroup ? (
        <>
          <Button
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpen(true)}
            variant="ghost"
          >
            <span>Change Group</span>
            <BoxIcon className="size-4 text-muted-foreground" />
          </Button>
          <Button
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => handleRemoveGroupFromArticle(articleId)}
            variant="ghost"
          >
            <span>Remove Group</span>
            <SquareXIcon className="size-4 text-muted-foreground" />
          </Button>
        </>
      ) : (
        <Button
          className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
          onClick={() => setOpen(true)}
          variant="ghost"
        >
          <span>Add Group</span>
          <BoxIcon className="size-4 text-muted-foreground" />
        </Button>
      )}

      <CommandDialog
        className="rounded-xl border-none ring-1 ring-muted lg:min-w-2xl dark:bg-transparent"
        commandClassName=" dark:bg-background/20 dark:backdrop-blur-md dark:supports-backdrop-blur:bg-background/90"
        onOpenChange={setOpen}
        open={open}
      >
        <CommandInput
          className="h-14 text-lg"
          iconClassName="size-5 hidden"
          onValueChange={(value) => setSearchText(value)}
          placeholder={'Search groups...'}
        />

        <CommandList className="max-h-[65vh] dark:bg-transparent">
          <CommandEmpty>
            No results found for{' '}
            <span className="font-medium">"{searchText}"</span>.
          </CommandEmpty>
          <CommandGroup heading="Groups">
            {groups.map((group) => (
              <CommandItem
                key={group._id}
                onSelect={() => {
                  runCommand(() =>
                    handleAddGroupToArticle(articleId, group._id)
                  );
                }}
                value={group.name}
              >
                {group.name}
                {prevGroupId === group._id && (
                  <span className="ml-auto text-muted-foreground">
                    Current Group
                  </span>
                )}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
