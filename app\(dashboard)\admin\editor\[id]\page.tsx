import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery, preloadQuery } from 'convex/nextjs';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { PageContainer } from '@/components/custom/page-container';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import EditorForm from '@/features/dashboard/editor/editor-form';

type Props = {
  params: Promise<{ id: Id<'articles'> }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params;
  const article = await fetchQuery(
    api.articles.getArticle,
    { id },
    { token: await convexAuthNextjsToken() }
  );
  if (!article || 'success' in article) {
    return notFound();
  }
  return {
    title: `Article - ${article.title}`,
    description: article.description,
  };
}
export default async function EditorPage({ params }: Props) {
  const { id } = await params;
  const article = await preloadQuery(
    api.articles.getArticle,
    { id },
    { token: await convexAuthNextjsToken() }
  );
  if (!article || 'success' in article) {
    return notFound();
  }

  return (
    <PageContainer>
      <EditorForm preloadedArticle={article} />
    </PageContainer>
  );
}
